<template>
  <PhantomLayout title="Create Assessment">
    <!-- Full-width container -->
    <div class="w-full p-7 -mt-14">
      <!-- Back to Assessment List Button - Top Right -->
      <div class="flex justify-end mb-8">
        <button
          class="btn-phantom-secondary px-6 py-3 text-base"
          @click="navigation.goToAssessmentsList()"
        >
          <span class="flex items-center">
            <SvgIcon name="arrow-left" class="mr-2" />
            Back to Assessment List
          </span>
        </button>
      </div>

      <form class="space-y-12" @submit.prevent="createAssessment" novalidate>
        <!-- Basic Information and Skills Selection Side by Side -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Basic Information Section -->
          <AssessmentBasicInfo
            :form-data="formData"
            :errors="formErrors"
            @update:form-data="formData = $event"
          />

          <!-- Skills Section with Question Selection Mode Below -->
          <div class="space-y-8">
            <!-- Skills Section -->
            <AssessmentSkillsSelection
              :selected-skill-ids="selectedSkillIds"
              :skills="skills"
              :skills-loading="isLoading"
              :has-interacted-with-skills="hasInteractedWithSkills"
              :errors="formErrors"
              @update:selected-skill-ids="selectedSkillIds = $event"
              @skill-interaction="hasInteractedWithSkills = true"
            />

            <!-- Question Selection Mode Section -->
            <AssessmentQuestionMode
              :question-selection-mode="questionSelectionMode"
              @update:question-selection-mode="questionSelectionMode = $event"
            />
          </div>
        </div>
        <!-- Status Section -->
        <section class="w-full">
          <!-- Loading indicator -->
          <div
            v-if="isLoading"
            class="flex justify-center items-center py-8 bg-white/5 backdrop-blur-sm rounded-xl"
          >
            <div
              class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue"
            />
            <span class="ml-4 text-white text-lg">Creating assessment...</span>
          </div>

          <!-- Error/Success message - Only show error messages here -->
          <div v-if="message && !isSuccess" class="my-4 message-container">
            <div
              class="bg-red-500/10 border-red-500/30 px-6 py-4 rounded-xl border backdrop-blur-sm text-white"
            >
              {{ message }}
            </div>
          </div>

          <!-- Success Details Section
          <AssessmentSuccessDetails
            v-if="createdAssessmentDetails"
            :assessment="createdAssessmentDetails"
          /> -->
        </section>

        <!-- Add Fixed Questions Section (shown when fixed mode is selected and assessment not yet created) -->
        <AssessmentFixedQuestions
          v-if="questionSelectionMode === 'fixed' && !createdAssessmentDetails"
          :selected-skill-ids="selectedSkillIds"
          :created-assessment="createdAssessmentDetails"
          @update:selected-question-ids="selectedQuestionIds = $event"
          @update:selected-question-count="selectedQuestionCount = $event"

        />

        <!-- Submit button (hidden after assessment is created) -->
        <section v-if="!createdAssessmentDetails" class="w-full mt-8 mb-4">
          <div class="flex justify-end">
            <button
              type="submit"
              :disabled="isSubmitDisabled"
              class="btn-phantom px-6 py-3"
              :class="{
                'opacity-50 cursor-not-allowed': isSubmitDisabled,
              }"
            >
              <span class="flex items-center">
                <SvgIcon
                  v-if="isLoading"
                  name="spinner"
                  class="-ml-1 mr-2 text-white animate-spin"
                />
                <SvgIcon v-else name="plus" class="mr-2" />
                {{ isLoading ? "Creating..." : (questionSelectionMode === 'fixed' ? "Create Assessment with Selected Questions" : "Create Assessment") }}
              </span>
            </button>
          </div>

          <!-- Validation message for fixed mode -->
          <div v-if="questionSelectionMode === 'fixed' && selectedSkillIds.length > 0 && selectedQuestionCount === 0"
               class="mt-3 text-center">
            <p class="text-yellow-400 text-sm flex items-center justify-center">
              <SvgIcon name="warning" class="mr-2 text-yellow-400" />
              Please select questions from the "Add Fixed Questions" section below before creating the assessment
            </p>
          </div>
          

        </section>
      </form>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { api } from "@/services/api";
import { logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { decodeSkillId } from "@/utils/hashIds";
import { debug, info, warning } from "@/utils/logger";
import { useNavigation } from "@/composables";
import globalNotification from "@/composables/useNotification";
import PhantomLayout from "@/components/layout/Layout.vue";

import SvgIcon from "@/components/SvgIcon.vue";

// Assessment Components
import AssessmentBasicInfo from "@/components/assessments/AssessmentBasicInfo.vue";
import AssessmentSkillsSelection from "@/components/assessments/AssessmentSkillsSelection.vue";
import AssessmentQuestionMode from "@/components/assessments/AssessmentQuestionMode.vue";
import AssessmentFixedQuestions from "@/components/assessments/AssessmentFixedQuestions.vue";
// import AssessmentSuccessDetails from "@/components/assessments/AssessmentSuccessDetails.vue";

// Composables
const navigation = useNavigation();
const { showSuccess } = globalNotification;

// Question selection state for fixed assessments (managed locally)
const selectedQuestionIds = ref([]);
const selectedQuestionCount = ref(0);
const questionValidationStatus = ref({
  isValid: false,
  message: '',
  currentCounts: { easy: 0, intermediate: 0, advanced: 0 },
  requiredCounts: { easy: 6, intermediate: 6, advanced: 8 }
});

// Message handling
const { message, isSuccess, setErrorMessage, clearMessage } =
  useMessageHandler();

// Auto-scroll to error message when it appears
const scrollToMessage = () => {
  if (message.value && !isSuccess.value) {
    setTimeout(() => {
      const messageElement = document.querySelector(".message-container");
      if (messageElement) {
        messageElement.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }
    }, 100);
  }
};

// Enhanced error message setter with auto-scroll
const setErrorMessageWithScroll = (msg) => {
  setErrorMessage(msg);
  scrollToMessage();
};

// Form data - restructured for component usage
const formData = ref({
  name: "",
  description: "",
  duration: 30,
});

const selectedSkillIds = ref([]); // Changed to array for multiple selection
const questionSelectionMode = ref("dynamic"); // Default to dynamic mode
const hasInteractedWithSkills = ref(false); // Track if user has interacted with skills
const skills = ref([]);
const isLoading = ref(false);
const isLoadingQuestions = ref(false);
const createdAssessmentDetails = ref(null);

// Form validation errors
const formErrors = ref({});

// Computed property for valid selected skill IDs
const validSelectedSkillIds = computed(() => {
  return selectedSkillIds.value.filter(
    (id) => id !== null && id !== undefined && id !== ""
  );
});

// Computed property for submit button disabled state
const isSubmitDisabled = computed(() => {
  // Basic validation: loading states and skill selection
  if (isLoading.value || isLoadingQuestions.value || selectedSkillIds.value.length === 0) {
    return true;
  }

  // For fixed mode, require questions to be selected
  if (questionSelectionMode.value === "fixed") {
    return selectedQuestionCount.value === 0;
  }

  // For dynamic mode, only require skills
  return false;
});

// Clean up the selectedSkillIds array to remove null/undefined values
const cleanupSelectedSkillIds = () => {
  const originalLength = selectedSkillIds.value.length;
  selectedSkillIds.value = selectedSkillIds.value.filter(
    (id) => id !== null && id !== undefined && id !== ""
  );

  if (selectedSkillIds.value.length !== originalLength) {
    debug("Cleaned up selectedSkillIds, removed null/undefined values");
  }
};

// Fetch skills from API
const fetchSkills = async () => {
  try {
    isLoading.value = true;
    const response = await api.admin.getSkills({ limit: 100, offset: 0 });

    // Use standardized response data extraction
    const skillsData = extractResponseData(response);

    if (Array.isArray(skillsData)) {
      debug("Fetched skills from API", {
        skillsCount: skillsData.length,
        sampleData: skillsData.slice(0, 2),
      });

      // Basic filtering - only filter out null/undefined skills
      const filteredSkills = skillsData.filter((skill) => {
        // Basic validation - must have an object
        if (!skill) {
          warning("Filtering out null/undefined skill");
          return false;
        }

        return true;
      });

      // Normalize skills - keep both hash ID for frontend and numeric ID for API
      const normalizedSkills = filteredSkills
        .map((skill) => {
          debug("Processing skill for normalization", { skill });

          // For display purposes, use id_hash (for security/obfuscation) or fallback to id
          const displayId = skill.id_hash || skill.id;

          // For API calls, we need the numeric ID
          // If skill.id is numeric, use it; if it's a hash, we'll decode it later
          let numericId = null;

          // Check if skill.id is already a numeric ID
          if (typeof skill.id === "number" && skill.id > 0) {
            numericId = skill.id;
          } else if (typeof skill.id === "string" && /^\d+$/.test(skill.id)) {
            numericId = parseInt(skill.id, 10);
          }
          // If we have a numeric_id field, use that
          else if (
            skill.numeric_id &&
            (typeof skill.numeric_id === "number" ||
              /^\d+$/.test(skill.numeric_id))
          ) {
            numericId =
              typeof skill.numeric_id === "number"
                ? skill.numeric_id
                : parseInt(skill.numeric_id, 10);
          }
          // If we don't have a numeric ID but have a hash, we'll need to decode it
          else if (
            skill.id_hash ||
            (typeof skill.id === "string" && !/^\d+$/.test(skill.id))
          ) {
            // Mark this skill as needing hash decoding
            numericId = "NEEDS_DECODING";
          }

          if (!displayId) {
            warning("Skill without display ID", { skill });
            return null;
          }

          try {
            return {
              ...skill,
              id: displayId, // Use hash ID for frontend display and selection
              numericId: numericId, // Store numeric ID for API calls (or 'NEEDS_DECODING')
              name: skill.name || "Unnamed Skill",
            };
          } catch (e) {
            warning("Error normalizing skill", { error: e, skill });
            return null;
          }
        })
        .filter((skill) => skill !== null);

      skills.value = normalizedSkills;
      info("Skills loaded successfully", { skillsCount: skills.value.length });

      // Clean up any invalid selected skill IDs after skills are loaded
      cleanupSelectedSkillIds();
    } else {
      // Try to handle non-array responses more gracefully
      warning("Response is not an array", { skillsData });

      // If it's an object with items or data property, try to use that
      if (skillsData && typeof skillsData === "object") {
        const possibleArrays = ["items", "data", "skills", "results"];
        for (const prop of possibleArrays) {
          if (Array.isArray(skillsData[prop])) {
            debug(`Found skills array in response.${prop}`);
            skills.value = skillsData[prop];
            return;
          }
        }
      }

      // If we get here, we couldn't find a valid skills array
      warning("Invalid skills response format", { response });
      skills.value = [];
      setErrorMessage("No skills found or invalid response format");
    }
  } catch (error) {
    const errorInfo = extractErrorInfo(error);
    error("Error in fetchSkills", { error: errorInfo });
    logError(error, "fetchSkills");
    setErrorMessage(errorInfo.message || "Failed to fetch skills");
    skills.value = []; // Ensure skills is always an array
  } finally {
    isLoading.value = false;
  }
};

// Create assessment via API
const createAssessment = async () => {
  // Set interaction flag when form is submitted
  hasInteractedWithSkills.value = true;

  // Enhanced validation
  if (
    !formData.value.name ||
    !formData.value.description ||
    selectedSkillIds.value.length === 0
  ) {
    setErrorMessageWithScroll(
      "Please fill in all required fields and select at least one skill"
    );
    return;
  }

  // Additional validation for fixed mode
  if (questionSelectionMode.value === "fixed" && selectedQuestionCount.value === 0) {
    setErrorMessageWithScroll(
      "Please select questions before creating a fixed assessment"
    );
    // Scroll to the question selection section to make it visible to the user
    const questionSelectionElement = document.querySelector('.AssessmentFixedQuestions');
    if (questionSelectionElement) {
      questionSelectionElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    return;
  }

  // Additional validation for fixed mode - check difficulty distribution ONLY when creating
  if (questionSelectionMode.value === "fixed" && selectedQuestionCount.value > 0) {
    // Manually validate question distribution here (not during selection)
    const requiredCounts = { easy: 6, intermediate: 6, advanced: 8 };

    // We need to manually count the selected questions by difficulty
    // For now, just check if we have enough total questions
    const totalRequired = requiredCounts.easy + requiredCounts.intermediate + requiredCounts.advanced;

    if (selectedQuestionCount.value < totalRequired) {
      setErrorMessageWithScroll(
        "Please ensure your selection meets the minimum difficulty requirements (6 easy, 6 intermediate, 8 advanced questions)"
      );
      const questionSelectionElement = document.querySelector('.AssessmentFixedQuestions');
      if (questionSelectionElement) {
        questionSelectionElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return;
    }
  }

  // Additional validation
  if (formData.value.name.trim().length < 3) {
    setErrorMessageWithScroll(
      "Assessment name must be at least 3 characters long"
    );
    return;
  }

  if (formData.value.name.trim().length > 255) {
    setErrorMessageWithScroll(
      "Assessment name must be less than 255 characters"
    );
    return;
  }

  if (formData.value.description.trim().length < 20) {
    setErrorMessageWithScroll(
      "Assessment description must be at least 20 characters long"
    );
    return;
  }

  if (formData.value.description.trim().length > 2000) {
    setErrorMessageWithScroll(
      "Assessment description must be less than 2000 characters"
    );
    return;
  }

  if (selectedSkillIds.value.length > 10) {
    setErrorMessageWithScroll(
      "Please select no more than 10 skills for an assessment"
    );
    return;
  }

  // Validate valid selected skill IDs
  const validSkillCount = validSelectedSkillIds.value.length;
  if (validSkillCount === 0) {
    setErrorMessageWithScroll("Please select at least one valid skill");
    return;
  }

  isLoading.value = true;
  clearMessage(); // Clear any existing messages
  createdAssessmentDetails.value = null;

  try {
    // Get the primary skill for the topic (using the first selected skill)
    const primarySkillId = selectedSkillIds.value[0];
    const primarySkill = skills.value.find(
      (skill) => skill.id === primarySkillId
    );

    if (!primarySkill) {
      throw new Error("Selected skill not found");
    }

    // Get current username (in a real app, this would come from auth)
    const username = localStorage.getItem("username") || "admin_user";

    // Convert hash IDs to numeric IDs for API (preserving the hash system)
    const skillsToProcess = validSelectedSkillIds.value
      .map((hashId) => {
        if (hashId === null || hashId === undefined || hashId === "") {
          return null;
        }

        // Find the skill by hash ID
        const skill = skills.value.find((skill) => skill.id === hashId);
        if (!skill) {
          warning(`Skill with hash ID ${hashId} not found in loaded skills`);
          return null;
        }

        return { hashId, skill };
      })
      .filter((item) => item !== null);

    if (skillsToProcess.length === 0) {
      throw new Error("No valid skills selected.");
    }

    // Process each skill to get numeric ID (decode if necessary)
    const validSkillIds = [];

    for (const { hashId, skill } of skillsToProcess) {
      let numericId = null;

      // If we already have a numeric ID, use it
      if (
        skill.numericId &&
        skill.numericId !== "NEEDS_DECODING" &&
        !isNaN(skill.numericId)
      ) {
        numericId = skill.numericId;
      }
      // If we need to decode, decode the hash ID
      else if (skill.numericId === "NEEDS_DECODING" || !skill.numericId) {
        try {
          numericId = await decodeSkillId(hashId);
          if (!numericId || isNaN(numericId)) {
            warning(
              `Failed to decode skill hash ${hashId} or got invalid numeric ID`,
              { hashId, numericId }
            );
            continue;
          }
        } catch (error) {
          warning(`Error decoding skill hash ${hashId}`, { hashId, error });
          continue;
        }
      }

      if (numericId && !isNaN(numericId) && numericId > 0) {
        validSkillIds.push(numericId);

        // Store the decoded numeric ID back to the skill object for future lookups
        if (skill.numericId === "NEEDS_DECODING" || !skill.numericId) {
          skill.numericId = numericId;
        }
      } else {
        warning(`Skill ${hashId} doesn't have a valid numeric ID`, {
          hashId,
          numericId,
        });
      }
    }

    if (validSkillIds.length === 0) {
      throw new Error(
        "No valid numeric skill IDs found. Please check that skills have proper numeric IDs or hash decoding is working."
      );
    }

    // Validate that all IDs are integers (API requirement)
    const nonIntegerIds = validSkillIds.filter((id) => !Number.isInteger(id));
    if (nonIntegerIds.length > 0) {
      throw new Error(
        `Some skill IDs are not integers: ${nonIntegerIds.join(", ")}`
      );
    }

    // Validate duration
    const durationValue = parseInt(formData.value.duration);
    if (isNaN(durationValue) || durationValue < 5 || durationValue > 180) {
      throw new Error(
        "Duration must be a valid number between 5 and 180 minutes"
      );
    }

    debug("Assessment creation skill mapping", {
      availableSkills: skills.value.map((s) => ({
        hashId: s.id,
        numericId: s.numericId,
        name: s.name,
      })),
      selectedSkillIds: selectedSkillIds.value,
      convertedNumericIds: validSkillIds,
      idConversionMapping: validSelectedSkillIds.value.map((hashId) => {
        const skill = skills.value.find((s) => s.id === hashId);
        return { hashId, numericId: skill?.numericId, name: skill?.name };
      }),
    });
    debug("Assessment data to send", {
      quiz_name: formData.value.name,
      topic: formData.value.description,
      user_id: username,
      skill_ids: validSkillIds,
      question_selection_mode: questionSelectionMode.value,
      duration: durationValue,
      create_single_assessment: true,
    });

    // Call the API to create the quiz/assessment
    const response = await api.admin.createAssessment({
      quiz_name: formData.value.name.trim(),
      topic: formData.value.description.trim(), // Use the new description field instead of skill description
      user_id: username.trim(),
      skill_ids: validSkillIds,
      question_selection_mode: questionSelectionMode.value,
      duration: durationValue, // Add duration in minutes
      create_single_assessment: true, // Create only one assessment instead of mock and final
    });

    // Use standardized response data extraction
    const responseData = extractResponseData(response);

    // Debug log the response data
    console.log("Assessment creation response:", responseData);

    // Store the response details for display
    createdAssessmentDetails.value = responseData;

    // Show success notification with action to generate sessions
    showSuccess("Assessment created successfully! Go to Generate Sessions", {
      duration: 7000, // Match the default in Notification.vue
      actionText: "Go to Generate Sessions",
      onAction: () => navigation.navigateTo("/generate-sessions"),
    });

    // If fixed mode and questions are selected, automatically add them
    if (questionSelectionMode.value === "fixed" && selectedQuestionCount.value > 0) {
      try {
        await addFixedQuestions();
        // Success message is shown via the green box with Generate Sessions button
      } catch (error) {
        // Error is already handled in addFixedQuestions function
        warning("Failed to auto-add selected questions", { error });
        setErrorMessage(
          `Assessment created, but failed to add questions. Please add them manually.`
        );
      }
    }
    // No need to set success message as we're showing the green success box instead

    // Reset form completely after success
    formData.value = {
      name: "",
      description: "",
      duration: 30,
    };
    selectedSkillIds.value = [];
    selectedQuestionIds.value = [];
    selectedQuestionCount.value = 0;
    questionSelectionMode.value = "dynamic"; // Reset to default mode
  } catch (error) {
    const errorInfo = extractErrorInfo(error);
    logError(error, "createAssessment");

    // Check if the error is related to skills not being found
    if (errorInfo.message && errorInfo.message.includes("not found")) {
      setErrorMessageWithScroll(
        "Some selected skills no longer exist in the database. Please refresh the page and reselect your skills."
      );
      // Automatically refresh skills to get the latest data
      try {
        await fetchSkills();
        debug("Refreshed skills after error");
      } catch (refreshError) {
        warning("Failed to refresh skills:", refreshError);
      }
    } else {
      setErrorMessageWithScroll(
        errorInfo.message ||
          "An unexpected error occurred while creating the assessment"
      );
    }

    createdAssessmentDetails.value = null;
  } finally {
    isLoading.value = false;
  }
};

// Add selected questions to the fixed assessment
const addFixedQuestions = async () => {
  if (!createdAssessmentDetails.value?.assessment_id || selectedQuestionIds.value.length === 0) {
    return;
  }

  try {
    // Ensure assessment_id is an integer
    const assessmentId = typeof createdAssessmentDetails.value.assessment_id === 'number' 
      ? createdAssessmentDetails.value.assessment_id 
      : parseInt(createdAssessmentDetails.value.assessment_id, 10);
    
    if (isNaN(assessmentId) || assessmentId <= 0) {
      throw new Error(`Invalid assessment ID: ${createdAssessmentDetails.value.assessment_id}`);
    }
    
    // Clean up and validate selected question IDs before processing
    debug("Selected question IDs before validation:", selectedQuestionIds.value);
    
    // Convert all question IDs to integers to match backend expectations
    const intQuestionIds = selectedQuestionIds.value
      .map(id => {
        // Handle various input types: number, string, or object with que_id property
        if (typeof id === 'number') {
          return id;
        } else if (typeof id === 'string') {
          const parsed = parseInt(id, 10);
          return isNaN(parsed) ? null : parsed;
        } else if (typeof id === 'object' && id !== null && id.que_id) {
          const parsed = typeof id.que_id === 'number' ? id.que_id : parseInt(id.que_id, 10);
          return isNaN(parsed) ? null : parsed;
        }
        return null;
      })
      .filter(id => id !== null && Number.isInteger(id) && id > 0); // Filter out any invalid IDs
    
    if (intQuestionIds.length === 0) {
      throw new Error('No valid question IDs to add. Please ensure you have selected valid questions.');
    }
    
    // Remove duplicates to ensure unique question IDs
    const uniqueQuestionIds = [...new Set(intQuestionIds)];
    
    // Validate difficulty distribution before sending to backend
    // This provides better error messages to users
    const validationResult = validateQuestionDifficultyDistribution(uniqueQuestionIds);
    if (!validationResult.isValid) {
      throw new Error(validationResult.message);
    }
    
    const payload = {
      assessment_id: assessmentId,
      question_ids: uniqueQuestionIds,
      quiz_name: formData.value.name.trim()
    };

    console.log("Adding fixed questions payload:", payload);
    debug("Selected question IDs before processing:", selectedQuestionIds.value);
    debug("Processed question IDs:", uniqueQuestionIds);

    const response = await api.admin.addFinalQuestions(payload);

    const responseData = extractResponseData(response);
    info("Successfully added questions to fixed assessment", {
      assessmentId: assessmentId,
      questionCount: uniqueQuestionIds.length
    });

    return responseData;
  } catch (error) {
    logError(error, "addFixedQuestions");
    throw error; // Re-throw to be handled by the caller
  }
};

// Helper function to validate question difficulty distribution
const validateQuestionDifficultyDistribution = (questionIds) => {
  // Use the validation status from the component
  if (!questionValidationStatus.value.isValid) {
    return {
      isValid: false,
      message: questionValidationStatus.value.message || 
        `Please ensure your selection meets the minimum difficulty requirements. Required: 6 easy, 6 intermediate, 8 advanced questions.`
    };
  }
  
  return { isValid: true };
};

onMounted(() => {
  fetchSkills();
});
</script>
