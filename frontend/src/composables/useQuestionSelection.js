/**
 * Question Selection Composable
 *
 * Provides reusable logic for question selection and management
 * Used in assessment creation and editing
 */
import { ref, computed } from "vue";
import { api } from "@/services/api";
import { useMessageHandler } from "@/utils/messageHandler";
import { useLoadingState } from "./useUIState";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import {
  logApiRequest,
  logApiResponse,
  logApiError,
  logUserAction,
} from "@/utils/logger";

export function useQuestionSelection() {
  const loadingState = useLoadingState();
  const messageHandler = useMessageHandler();

  // Question data
  const availableQuestions = ref([]);
  const selectedQuestionIds = ref([]);
  const questionCounts = ref({
    easy: 6,
    intermediate: 6,
    advanced: 8,
  });

  // Filters
  const searchQuery = ref("");
  const difficultyFilter = ref("all");

  // Computed properties
  const filteredQuestions = computed(() => {
    let filtered = availableQuestions.value;

    // Filter by search query
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(
        (q) =>
          q.question?.toLowerCase().includes(query) ||
          q.skill_name?.toLowerCase().includes(query),
      );
    }

    // Filter by difficulty
    if (difficultyFilter.value !== "all") {
      filtered = filtered.filter((q) => q.level === difficultyFilter.value);
    }

    // Sort by difficulty level: easy → intermediate → advanced
    const difficultyOrder = { easy: 1, intermediate: 2, advanced: 3 };
    filtered = filtered.sort((a, b) => {
      const orderA = difficultyOrder[a.level] || 999;
      const orderB = difficultyOrder[b.level] || 999;
      return orderA - orderB;
    });

    return filtered;
  });

  const selectedQuestionCount = computed(
    () => selectedQuestionIds.value.length,
  );

  const totalRequiredQuestions = computed(
    () =>
      questionCounts.value.easy +
      questionCounts.value.intermediate +
      questionCounts.value.advanced,
  );

  const questionCountsByLevel = computed(() => {
    const counts = { easy: 0, intermediate: 0, advanced: 0 };

    selectedQuestionIds.value.forEach((id) => {
      const question = availableQuestions.value.find((q) => q.que_id === id);
      if (question?.level) {
        counts[question.level]++;
      }
    });

    return counts;
  });

  // No validation logic - removed to prevent any validation messages

  /**
   * Fetch questions for selected skills
   */
  const fetchQuestionsForSkills = async (skillIds) => {
    if (!Array.isArray(skillIds) || skillIds.length === 0) {
      availableQuestions.value = [];
      return;
    }

    loadingState.startLoading("Loading questions...");

    try {
      const allQuestions = [];

      for (const skillId of skillIds) {
        try {
          logApiRequest("GET", `/skills/${skillId}/questions`);
          const response = await api.admin.getSkillQuestions(skillId);
          logApiResponse(
            "GET",
            `/skills/${skillId}/questions`,
            response.status,
          );

          const responseData = extractResponseData(response);

          if (responseData?.questions) {
            const questionsWithSkillName = responseData.questions.map(
              (question) => ({
                ...question,
                skill_name: responseData.skill_name || `Skill ${skillId}`,
              }),
            );
            allQuestions.push(...questionsWithSkillName);
          }
        } catch (error) {
          logApiError("GET", `/skills/${skillId}/questions`, error);
        }
      }

      // Remove duplicates based on question ID
      const uniqueQuestions = allQuestions.filter(
        (question, index, self) =>
          index === self.findIndex((q) => q.que_id === question.que_id),
      );

      availableQuestions.value = uniqueQuestions;

      logUserAction("questions_loaded", {
        skillCount: skillIds.length,
        questionCount: uniqueQuestions.length,
      });
    } catch (error) {
      logApiError("GET", "/questions", error);
      const errorInfo = extractErrorInfo(error);
      messageHandler.setErrorMessage(
        errorInfo.message || "Failed to fetch questions",
      );
    } finally {
      loadingState.stopLoading();
    }
  };

  /**
   * Check if a question is selected
   */
  const isQuestionSelected = (questionId) => {
    // Handle various input types consistently
    let intQuestionId;
    
    if (typeof questionId === 'number') {
      intQuestionId = questionId;
    } else if (typeof questionId === 'string') {
      intQuestionId = parseInt(questionId, 10);
    } else if (typeof questionId === 'object' && questionId !== null && questionId.que_id) {
      intQuestionId = typeof questionId.que_id === 'number' ? questionId.que_id : parseInt(questionId.que_id, 10);
    } else {
      console.warn('Invalid question ID provided to isQuestionSelected:', questionId);
      return false;
    }
    
    if (isNaN(intQuestionId)) {
      console.warn('Invalid question ID after parsing in isQuestionSelected:', intQuestionId, 'from original:', questionId);
      return false;
    }
    
    // Check both the parsed integer ID and the original ID
    return selectedQuestionIds.value.includes(intQuestionId) || selectedQuestionIds.value.includes(questionId);
  };

  /**
   * Toggle question selection
   */
  const toggleQuestionSelection = (questionId) => {
    if (isQuestionSelected(questionId)) {
      removeQuestionFromSelection(questionId);
    } else {
      addQuestionToSelection(questionId);
    }
  };

  /**
   * Add question to selection
   */
  const addQuestionToSelection = (questionId) => {
    // Convert questionId to integer to ensure consistency
    let intQuestionId;
    
    if (typeof questionId === 'number') {
      intQuestionId = questionId;
    } else if (typeof questionId === 'string') {
      intQuestionId = parseInt(questionId, 10);
    } else if (typeof questionId === 'object' && questionId !== null && questionId.que_id) {
      intQuestionId = typeof questionId.que_id === 'number' ? questionId.que_id : parseInt(questionId.que_id, 10);
    } else {
      console.warn('Invalid question ID provided to addQuestionToSelection:', questionId);
      return;
    }
    
    if (isNaN(intQuestionId) || intQuestionId <= 0) {
      console.warn('Invalid question ID after parsing:', intQuestionId, 'from original:', questionId);
      return;
    }
    
    if (!isQuestionSelected(intQuestionId)) {
      selectedQuestionIds.value.push(intQuestionId);

      const question = availableQuestions.value.find(
        (q) => q.que_id === questionId || q.que_id === intQuestionId,
      );
      logUserAction("question_selected", {
        questionId: intQuestionId,
        level: question?.level,
        skillName: question?.skill_name,
      });
    }
  };

  /**
   * Remove question from selection
   */
  const removeQuestionFromSelection = (questionId) => {
    // Convert to integer for consistency, handling various input types
    let intQuestionId;
    
    if (typeof questionId === 'number') {
      intQuestionId = questionId;
    } else if (typeof questionId === 'string') {
      intQuestionId = parseInt(questionId, 10);
    } else if (typeof questionId === 'object' && questionId !== null && questionId.que_id) {
      intQuestionId = typeof questionId.que_id === 'number' ? questionId.que_id : parseInt(questionId.que_id, 10);
    } else {
      console.warn('Invalid question ID provided to removeQuestionFromSelection:', questionId);
      return;
    }
    
    if (isNaN(intQuestionId)) {
      console.warn('Invalid question ID after parsing in removeQuestionFromSelection:', intQuestionId, 'from original:', questionId);
      return;
    }
    
    // Try to find the question ID in the selected list
    let index = selectedQuestionIds.value.indexOf(intQuestionId);
    if (index === -1) {
      // Also try with the original questionId in case of type mismatch
      index = selectedQuestionIds.value.indexOf(questionId);
    }
    
    if (index > -1) {
      selectedQuestionIds.value.splice(index, 1);

      const question = availableQuestions.value.find(
        (q) => q.que_id === questionId || q.que_id === intQuestionId,
      );
      logUserAction("question_deselected", {
        questionId: intQuestionId,
        level: question?.level,
        skillName: question?.skill_name,
      });
    }
  };

  /**
   * Clear all selected questions
   */
  const clearQuestionSelection = () => {
    const previousCount = selectedQuestionIds.value.length;
    selectedQuestionIds.value = [];

    logUserAction("questions_cleared", { previousCount });
  };

  /**
   * Randomly select questions based on required counts
   */
  const selectRandomQuestions = () => {
    clearQuestionSelection();

    const questionsByLevel = {
      easy: availableQuestions.value.filter((q) => q.level === "easy"),
      intermediate: availableQuestions.value.filter(
        (q) => q.level === "intermediate",
      ),
      advanced: availableQuestions.value.filter((q) => q.level === "advanced"),
    };

    // Randomly select questions for each level
    Object.keys(questionCounts.value).forEach((level) => {
      const questionsForLevel = questionsByLevel[level];
      const requiredCount = questionCounts.value[level];

      if (questionsForLevel.length >= requiredCount) {
        const shuffled = [...questionsForLevel].sort(() => 0.5 - Math.random());
        const selected = shuffled.slice(0, requiredCount);
        selected.forEach((q) => {
          // Ensure que_id is properly extracted and converted to integer
          let questionId;
          if (typeof q.que_id === 'number') {
            questionId = q.que_id;
          } else if (typeof q.que_id === 'string') {
            questionId = parseInt(q.que_id, 10);
          } else if (typeof q === 'object' && q !== null) {
            // Handle case where the question object might have different structure
            questionId = q.id || q.que_id || q.question_id;
            if (typeof questionId === 'string') {
              questionId = parseInt(questionId, 10);
            }
          }
          
          if (!isNaN(questionId) && questionId > 0) {
            addQuestionToSelection(questionId);
          } else {
            console.warn('Invalid question ID found during random selection:', q);
          }
        });
      }
    });

    logUserAction("questions_randomly_selected", {
      totalSelected: selectedQuestionIds.value.length,
      requiredCounts: questionCounts.value,
    });
  };

  /**
   * Validate and clean up selected question IDs
   */
  const validateAndCleanSelectedQuestions = () => {
    const originalLength = selectedQuestionIds.value.length;
    
    // Clean up the array by ensuring all IDs are valid integers
    selectedQuestionIds.value = selectedQuestionIds.value
      .map(id => {
        if (typeof id === 'number' && Number.isInteger(id) && id > 0) {
          return id;
        } else if (typeof id === 'string') {
          const parsed = parseInt(id, 10);
          return (!isNaN(parsed) && parsed > 0) ? parsed : null;
        } else if (typeof id === 'object' && id !== null && id.que_id) {
          const parsed = typeof id.que_id === 'number' ? id.que_id : parseInt(id.que_id, 10);
          return (!isNaN(parsed) && parsed > 0) ? parsed : null;
        }
        return null;
      })
      .filter(id => id !== null);
      
    // Remove duplicates
    selectedQuestionIds.value = [...new Set(selectedQuestionIds.value)];
    
    if (selectedQuestionIds.value.length !== originalLength) {
      console.warn(`Cleaned up selected question IDs: ${originalLength} -> ${selectedQuestionIds.value.length}`);
      logUserAction("questions_cleaned", {
        originalLength,
        cleanedLength: selectedQuestionIds.value.length
      });
    }
    
    return selectedQuestionIds.value;
  };

  /**
   * Update question count requirements
   */
  const updateQuestionCounts = (newCounts) => {
    questionCounts.value = { ...questionCounts.value, ...newCounts };

    logUserAction("question_counts_updated", { newCounts });
  };

  /**
   * Reset selection state
   */
  const resetSelection = () => {
    availableQuestions.value = [];
    selectedQuestionIds.value = [];
    searchQuery.value = "";
    difficultyFilter.value = "all";

    logUserAction("question_selection_reset");
  };

  return {
    // State
    availableQuestions,
    selectedQuestionIds,
    questionCounts,
    searchQuery,
    difficultyFilter,

    // Computed
    filteredQuestions,
    selectedQuestionCount,
    totalRequiredQuestions,
    questionCountsByLevel,

    // Loading only (no messaging to prevent validation messages)
    ...loadingState,

    // Methods
    fetchQuestionsForSkills,
    isQuestionSelected,
    toggleQuestionSelection,
    addQuestionToSelection,
    removeQuestionFromSelection,
    clearQuestionSelection,
    selectRandomQuestions,
    updateQuestionCounts,
    resetSelection,
    validateAndCleanSelectedQuestions,
  };
}
