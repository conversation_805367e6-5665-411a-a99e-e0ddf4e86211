<template>
  <section
    class="AssessmentFixedQuestions bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8 mt-8"
  >
    <h2
      class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-indigo to-phantom-purple bg-clip-text text-transparent"
    >
      Add Fixed Questions
    </h2>

    <!-- Instructions -->
    <div
      class="mb-6 bg-phantom-indigo/10 backdrop-blur-sm border border-phantom-indigo/20 rounded-xl p-5"
    >
      <p class="text-white/90 flex items-start">
        <SvgIcon
          name="warning"
          class="mr-2 flex-shrink-0 mt-0.5 text-phantom-indigo"
        />
        <span>
          <strong>Important:</strong> First select your questions below, then click the "Create Assessment with Selected Questions" button at the bottom of the page. The assessment will be created with your selected questions.
        </span>
      </p>
    </div>

    <!-- Question Distribution -->
    <div class="mb-8">
      <h3 class="text-lg font-medium text-gray-300 mb-4">
        Question Distribution
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-gray-800/50 p-4 rounded-lg">
          <label
            for="easy-questions"
            class="text-green-400 font-medium block mb-2"
          >
            Easy Questions
          </label>
          <input
            id="easy-questions"
            v-model.number="questionCounts.easy"
            name="easy-questions"
            type="number"
            min="6"
            autocomplete="off"
            placeholder="6"
            class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50"
          />
          <div class="mt-1 text-xs text-gray-400">
            Minimum: 6 questions
          </div>
        </div>

        <div class="bg-gray-800/50 p-4 rounded-lg">
          <label
            for="intermediate-questions"
            class="text-yellow-400 font-medium block mb-2"
          >
            Intermediate Questions
          </label>
          <input
            id="intermediate-questions"
            v-model.number="questionCounts.intermediate"
            name="intermediate-questions"
            type="number"
            min="6"
            autocomplete="off"
            placeholder="6"
            class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50"
          />
          <div class="mt-1 text-xs text-gray-400">
            Minimum: 6 questions
          </div>
        </div>

        <div class="bg-gray-800/50 p-4 rounded-lg">
          <label
            for="advanced-questions"
            class="text-red-400 font-medium block mb-2"
          >
            Advanced Questions
          </label>
          <input
            id="advanced-questions"
            v-model.number="questionCounts.advanced"
            name="advanced-questions"
            type="number"
            min="8"
            autocomplete="off"
            placeholder="8"
            class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50"
          />
          <div class="mt-1 text-xs text-gray-400">
            Minimum: 8 questions
          </div>
        </div>
      </div>
      <div class="mt-3 text-sm text-gray-400">
        Specify the number of questions for each difficulty level. You can
        add more than the minimum requirements.
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-6">
      <div
        class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-phantom-blue"
      />
      <span class="ml-4 text-white/80">Loading questions...</span>
    </div>

    <!-- No Questions Available -->
    <div
      v-else-if="availableQuestions.length === 0"
      class="text-center py-6 text-white/60"
    >
      <p>No questions available for the selected skills.</p>
      <p class="text-sm mt-2">Please add questions to these skills first.</p>
    </div>

    <!-- Questions Interface -->
    <div v-else class="space-y-6">
      <!-- Selected Questions Summary -->
      <div class="mb-6">
        <div class="flex justify-between items-center mb-3">
          <h3 class="text-lg font-medium text-gray-300">
            Selected Questions
          </h3>
          <span
            class="text-indigo-300 bg-indigo-900/30 px-3 py-1 rounded-full text-sm"
          >
            {{ selectedQuestionCount }} selected
          </span>
        </div>

        <div class="bg-gray-800/50 p-4 min-h-[80px] rounded-lg">
          <div
            v-if="selectedQuestionCount === 0"
            class="text-gray-500 text-sm italic flex items-center justify-center h-16"
          >
            No questions selected. Select questions from the list below.
          </div>
          <div v-else class="flex flex-wrap gap-2">
            <div
              v-for="id in selectedQuestionIds"
              :key="id"
              class="bg-indigo-900/30 text-indigo-300 px-3 py-1 rounded-full text-sm flex items-center"
            >
              ID: {{ id }}
              <button
                type="button"
                class="ml-2 text-indigo-400 hover:text-white"
                @click="removeQuestionFromSelection(id)"
              >
                ×
              </button>
            </div>
          </div>
        </div>

        <!-- Selection Status -->
        <div class="mt-2">
          <div class="text-sm text-gray-400">
            <span v-if="selectedQuestionCount > 0">
              {{ selectedQuestionCount }} questions selected
              <span
                v-if="totalRequiredQuestions > 0"
                :class="{
                  'text-green-400': selectedQuestionCount === totalRequiredQuestions,
                  'text-yellow-400': selectedQuestionCount < totalRequiredQuestions,
                  'text-red-400': selectedQuestionCount > totalRequiredQuestions,
                }"
              >
                ({{ totalRequiredQuestions }} required)
              </span>
            </span>
            <span v-else class="text-yellow-400">
              Please select questions before creating the assessment
            </span>
          </div>
          
          <!-- Difficulty Distribution Status -->
          <div v-if="selectedQuestionCount > 0" class="mt-3 space-y-2">
            <div class="text-sm font-medium text-gray-300">Current Selection by Difficulty:</div>
            <div class="flex gap-4 text-xs">
              <div class="flex items-center">
                <span class="w-3 h-3 rounded-full mr-2" 
                      :class="questionCountsByLevel.easy >= questionCounts.easy ? 'bg-green-500' : 'bg-gray-500'"></span>
                <span :class="questionCountsByLevel.easy >= questionCounts.easy ? 'text-green-400' : 'text-gray-400'">
                  Easy: {{ questionCountsByLevel.easy }}/{{ questionCounts.easy }}
                </span>
              </div>
              <div class="flex items-center">
                <span class="w-3 h-3 rounded-full mr-2" 
                      :class="questionCountsByLevel.intermediate >= questionCounts.intermediate ? 'bg-green-500' : 'bg-gray-500'"></span>
                <span :class="questionCountsByLevel.intermediate >= questionCounts.intermediate ? 'text-green-400' : 'text-gray-400'">
                  Intermediate: {{ questionCountsByLevel.intermediate }}/{{ questionCounts.intermediate }}
                </span>
              </div>
              <div class="flex items-center">
                <span class="w-3 h-3 rounded-full mr-2" 
                      :class="questionCountsByLevel.advanced >= questionCounts.advanced ? 'bg-green-500' : 'bg-gray-500'"></span>
                <span :class="questionCountsByLevel.advanced >= questionCounts.advanced ? 'text-green-400' : 'text-gray-400'">
                  Advanced: {{ questionCountsByLevel.advanced }}/{{ questionCounts.advanced }}
                </span>
              </div>
            </div>
            
            <!-- Selection Info -->
            <div class="text-xs text-gray-400 flex items-center">
              <SvgIcon name="info" class="mr-1 w-3 h-3" />
              <span>{{ selectedQuestionCount }} questions selected</span>
            </div>
          </div>
        </div>
      </div>

      <!-- No Questions Available Message -->
      <div
        v-if="
          selectedSkillIds.length > 0 &&
          availableQuestions.length === 0 &&
          !isLoading
        "
        class="mb-6 bg-yellow-900/20 p-4 rounded-lg"
      >
        <p class="text-yellow-300 flex items-start">
          <SvgIcon
            name="warning"
            class="mr-2 flex-shrink-0 mt-0.5 text-yellow-300"
          />
          <span>
            No questions available for the selected skills. Please select
            different skills or add questions to these skills first.
          </span>
        </p>
      </div>

      <!-- Available Questions -->
      <div v-if="availableQuestions.length > 0" class="mb-6">
        <div
          class="flex flex-col md:flex-row md:justify-between md:items-center mb-4 space-y-3 md:space-y-0"
        >
          <h3 class="text-lg font-medium text-gray-300">
            Available Questions
          </h3>

          <!-- Random Selection Button -->
          <Button
            variant="generalAction"
            size="skillButton"
            :disabled="availableQuestions.length === 0"
            title="Randomly select questions based on assessment requirements"
            class="md:ml-auto"
            @click.prevent="selectRandomQuestions"
          >
            <span class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Random Select
            </span>
          </Button>
        </div>

        <!-- Search and Filter -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div class="md:col-span-3">
            <input
              id="search-questions"
              v-model="searchQuery"
              name="search-questions"
              placeholder="Search questions..."
              class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50"
            />
          </div>
          <div>
            <select
              id="difficulty-filter"
              v-model="difficultyFilter"
              name="difficulty-filter"
              class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 [&>option]:bg-gray-800 [&>option]:text-white"
            >
              <option value="all">All Difficulties</option>
              <option value="easy">Easy</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
            </select>
          </div>
        </div>

        <div class="bg-gray-800/30 p-4 max-h-[500px] overflow-y-auto rounded-lg">
          <div
            v-if="filteredQuestions.length === 0"
            class="text-center py-8 text-gray-400"
          >
            No questions match your filters.
          </div>
          <div v-else class="space-y-4">
            <div
              v-for="question in filteredQuestions"
              :key="question.que_id"
              class="p-4 bg-gray-800/50 hover:bg-gray-800/70 transition-all rounded-lg"
              :class="{
                'bg-indigo-900/20 border-l-4 border-l-indigo-500':
                  isQuestionSelected(question.que_id),
              }"
            >
              <div
                class="flex flex-col md:flex-row md:justify-between md:items-start gap-3"
              >
                <div class="flex-1">
                  <div class="flex flex-wrap items-center gap-2 mb-2">
                    <span
                      class="px-2 py-1 text-xs rounded-full"
                      :class="{
                        'bg-green-900/50 text-green-400':
                          question.level === 'easy',
                        'bg-yellow-900/50 text-yellow-400':
                          question.level === 'intermediate',
                        'bg-red-900/50 text-red-400':
                          question.level === 'advanced',
                      }"
                    >
                      {{
                        question.level.charAt(0).toUpperCase() +
                        question.level.slice(1)
                      }}
                    </span>
                    <span class="text-gray-400 text-sm"
                      >ID: {{ question.que_id }}</span
                    >
                    <span class="text-gray-400 text-sm">{{
                      question.skill_name
                    }}</span>
                  </div>
                  <div class="text-white">
                    {{ question.question }}
                  </div>
                </div>
                <div class="md:ml-4 flex-shrink-0">
                  <Button
                    variant="generalAction"
                    size="skillButton"
                    :class="
                      isQuestionSelected(question.que_id)
                        ? 'bg-indigo-700/70 text-indigo-200 hover:bg-indigo-600/70'
                        : 'bg-indigo-900/50 text-indigo-400 hover:bg-indigo-800/50'
                    "
                    @click="addQuestionToSelection(question.que_id)"
                  >
                    {{
                      isQuestionSelected(question.que_id)
                        ? "Selected"
                        : "Select"
                    }}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
/**
 * Assessment Fixed Questions Component
 * Handles question selection for fixed-mode assessments
 */
import { computed, watch, onMounted } from "vue";
import { useQuestionSelection } from "@/composables";
import SvgIcon from "@/components/SvgIcon.vue";
import { Button } from "@/components/ui/button";

const props = defineProps({
  selectedSkillIds: {
    type: Array,
    required: true,
  },
  createdAssessment: {
    type: Object,
    default: null,
  },
});

// Define emits to communicate with parent component
const emit = defineEmits(['update:selected-question-ids', 'update:selected-question-count', 'update:validation-status']);

// Use question selection composable
const questionSelection = useQuestionSelection();

// Destructure composable state and methods
const {
  availableQuestions,
  questionCounts,
  searchQuery,
  difficultyFilter,
  filteredQuestions,
  selectedQuestionCount,
  selectedQuestionIds,
  questionCountsByLevel,
  isLoading,
  fetchQuestionsForSkills,
  isQuestionSelected,
  selectRandomQuestions,
  addQuestionToSelection,
  removeQuestionFromSelection,
} = questionSelection;

// Computed properties
const totalRequiredQuestions = computed(() => {
  return questionCounts.value.easy + questionCounts.value.intermediate + questionCounts.value.advanced;
});

// Watchers
watch(
  () => props.selectedSkillIds,
  (newSkillIds) => {
    if (newSkillIds.length > 0) {
      fetchQuestionsForSkills(newSkillIds);
    }
  },
  { immediate: true },
);

// Watch for changes in selected questions and emit to parent
watch(
  () => selectedQuestionIds.value,
  (newSelectedIds) => {
    console.log("AssessmentFixedQuestions: selectedQuestionIds changed", {
      newSelectedIds: newSelectedIds,
      types: newSelectedIds.map(id => ({ id, type: typeof id }))
    });
    emit('update:selected-question-ids', newSelectedIds);
  },
  { deep: true }
);

watch(
  () => selectedQuestionCount.value,
  (newCount) => {
    emit('update:selected-question-count', newCount);
  }
);

// Temporarily disable validation status emission to debug the issue
// watch(
//   () => questionCountsByLevel.value,
//   (currentCounts) => {
//     const requiredCounts = questionCounts.value;
//     const isValid = (
//       currentCounts.easy >= requiredCounts.easy &&
//       currentCounts.intermediate >= requiredCounts.intermediate &&
//       currentCounts.advanced >= requiredCounts.advanced
//     );

//     // Emit validation status without generating the message during selection
//     // The message will only be generated when creating the assessment
//     emit('update:validation-status', {
//       isValid,
//       message: '', // No message during selection
//       currentCounts,
//       requiredCounts
//     });
//   },
//   { immediate: true, deep: true }
// );

// Lifecycle
onMounted(() => {
  if (props.selectedSkillIds.length > 0) {
    fetchQuestionsForSkills(props.selectedSkillIds);
  }
});
</script>
